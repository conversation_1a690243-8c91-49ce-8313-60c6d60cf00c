/* Chat-specific styles */
.chat-container {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

.sidebar {
  width: 280px;
  flex-shrink: 0;
  background-color: #1a202c; /* Equivalent to bg-gray-900 */
  color: white;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  background-color: #2d3748; /* Equivalent to bg-gray-800 */
}

.chat-messages {
  flex-grow: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

.message-input-form {
  padding: 1rem;
  background-color: #1a202c; /* Equivalent to bg-gray-900 */
}

/* Custom scrollbar */
.chat-messages::-webkit-scrollbar {
  width: 8px;
}

.chat-messages::-webkit-scrollbar-track {
  background: #2d3748; /* bg-gray-800 */
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #4a5568; /* bg-gray-600 */
  border-radius: 4px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #718096; /* bg-gray-500 */
} 