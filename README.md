# SignalR Chat

This is a real-time chat application built with React, Vite, TypeScript, and SignalR. It features a modern user interface styled with Tailwind CSS.

## Features

- Real-time messaging
- User authentication
- Typing indicators
- Online user count
- Modern and responsive UI

## Prerequisites

- Node.js (v14 or later)
- .NET SDK (for running the SignalR backend)

## Getting Started

1.  **Clone the repository:**

    ```bash
    git clone <repository-url>
    cd Signal-Frontend
    ```

2.  **Install dependencies:**

    ```bash
    npm install
    ```

3.  **Configure the SignalR backend URL:**

    Create a `.env.local` file in the root of the project and add the following line:

    ```
    VITE_SIGNALR_HUB_URL=<your-signalr-hub-url>
    ```

    Replace `<your-signalr-hub-url>` with the URL of your SignalR hub (e.g., `http://localhost:5000/chatHub`).

4.  **Run the development server:**

    ```bash
    npm run dev
    ```

    This will start the Vite development server and the application will be available at `http://localhost:5173`.

## Building for Production

To create a production build, run the following command:

```bash
npm run build
```

This will generate a `dist` directory with the optimized and minified assets. 