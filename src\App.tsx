import React, { useState, useEffect, useRef } from 'react';
import * as signalR from '@microsoft/signalr';
import {
  PaperAirplaneIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon,
  UserCircleIcon,
  ChatBubbleLeftRightIcon,
  WifiIcon,
  NoSymbolIcon,
  UsersIcon,
} from '@heroicons/react/24/outline';
import './Chat.css';

interface Message {
  user: string;
  message: string;
  timestamp: string;
  id?: string;
  status?: 'sending' | 'sent' | 'delivered';
}

interface TypingUser {
  user: string;
  timestamp: number;
}

const App: React.FC = () => {
  const [connection, setConnection] = useState<signalR.HubConnection | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [user, setUser] = useState<string>('');
  const [message, setMessage] = useState<string>('');
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [isConnecting, setIsConnecting] = useState<boolean>(false);
  const [connectionError, setConnectionError] = useState<string>('');
  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([]);
  const [isTyping, setIsTyping] = useState<boolean>(false);
  const [onlineUsers, setOnlineUsers] = useState<number>(0);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<number | null>(null);
  const [isUserSet, setIsUserSet] = useState(false);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const getUserAvatarColor = (username: string) => {
    const colors = [
      'from-purple-500 to-indigo-500',
      'from-blue-500 to-cyan-500',
      'from-green-500 to-teal-500',
      'from-yellow-500 to-amber-500',
      'from-red-500 to-rose-500',
      'from-pink-500 to-fuchsia-500',
    ];
    const hash = username.split('').reduce((a, b) => a + b.charCodeAt(0), 0);
    return colors[hash % colors.length];
  };

  const getUserInitials = (username: string) => {
    return username.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  const handleTyping = () => {
    if (!isTyping && connection && isConnected) {
      setIsTyping(true);
      connection.invoke('UserTyping', user);
    }

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    typingTimeoutRef.current = window.setTimeout(() => {
      setIsTyping(false);
      if (connection && isConnected) {
        connection.invoke('UserStoppedTyping', user);
      }
    }, 2000);
  };

  useEffect(() => {
    const hubUrl = import.meta.env.VITE_SIGNALR_HUB_URL || '/chatHub';
    const newConnection = new signalR.HubConnectionBuilder()
      .withUrl(hubUrl, {
        skipNegotiation: false,
        transport: signalR.HttpTransportType.WebSockets | signalR.HttpTransportType.ServerSentEvents | signalR.HttpTransportType.LongPolling,
      })
      .withAutomaticReconnect([0, 2000, 10000, 30000])
      .configureLogging(signalR.LogLevel.Information)
      .build();

    setConnection(newConnection);

    return () => {
      if (newConnection) {
        newConnection.stop();
      }
    };
  }, []);

  useEffect(() => {
    if (connection) {
      setIsConnecting(true);
      connection
        .start()
        .then(() => {
          setIsConnected(true);
          setIsConnecting(false);
          setConnectionError('');
          connection.invoke('GetMessages');
        })
        .catch((err) => {
          setIsConnected(false);
          setIsConnecting(false);
          if (err.message.includes('CORS') || err.message.includes('NetworkError') || err.message.includes('Failed to complete negotiation')) {
            setConnectionError('CORS Error: Backend server needs to allow connections from this domain.');
          } else if (err.message.includes('timeout') || err.message.includes('refused')) {
            setConnectionError('Connection Error: Backend server might not be running.');
          } else {
            setConnectionError(`Connection Error: ${err.message}`);
          }
        });

      connection.on('ReceiveMessage', (user: string, message: string, timestamp: string) => {
        setMessages((prevMessages) => [
          ...prevMessages,
          { user, message, timestamp: new Date(timestamp).toLocaleString(), id: Date.now().toString(), status: 'delivered' },
        ]);
      });

      connection.on('LoadMessages', (messages: any[]) => {
        setMessages(
          messages.map((msg) => ({
            user: msg.user,
            message: msg.content || msg.message,
            timestamp: new Date(msg.timestamp).toLocaleString(),
            id: msg.id || Date.now().toString(),
            status: 'delivered' as const,
          }))
        );
      });

      connection.on('UserTyping', (username: string) => {
        if (username !== user) {
          setTypingUsers(prev => {
            const existing = prev.find(u => u.user === username);
            if (existing) {
              return prev.map(u => u.user === username ? { ...u, timestamp: Date.now() } : u);
            }
            return [...prev, { user: username, timestamp: Date.now() }];
          });
        }
      });

      connection.on('UserStoppedTyping', (username: string) => {
        setTypingUsers(prev => prev.filter(u => u.user !== username));
      });

      connection.on('UpdateOnlineUsers', (count: number) => {
        setOnlineUsers(count);
      });

      connection.onreconnecting(() => {
        setIsConnected(false);
        setIsConnecting(true);
      });

      connection.onreconnected(() => {
        setIsConnected(true);
        setIsConnecting(false);
      });

      connection.onclose(() => {
        setIsConnected(false);
        setIsConnecting(false);
      });
    }
  }, [connection, user]);

  const sendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (connection && user && message.trim() && isConnected) {
      const messageId = Date.now().toString();
      const messageText = message.trim();
      const newMessage: Message = { user, message: messageText, timestamp: new Date().toLocaleString(), id: messageId, status: 'sending' };
      setMessages(prev => [...prev, newMessage]);
      setMessage('');
      if (isTyping) {
        setIsTyping(false);
        if (typingTimeoutRef.current) clearTimeout(typingTimeoutRef.current);
        connection.invoke('UserStoppedTyping', user);
      }
      try {
        await connection.invoke('SendMessage', user, messageText);
        setMessages(prev => prev.map(msg => msg.id === messageId ? { ...msg, status: 'sent' } : msg));
      } catch (err) {
        console.error('Error sending message: ', err);
      }
    }
  };

  const retryConnection = () => {
    if (connection && !isConnected && !isConnecting) {
      setIsConnecting(true);
      connection.start().then(() => {
        setIsConnected(true);
        setIsConnecting(false);
        setConnectionError('');
        connection.invoke('GetMessages');
      }).catch(err => {
        setIsConnected(false);
        setIsConnecting(false);
        setConnectionError(`Failed to reconnect: ${err.message}`);
      });
    }
  };
  
  const handleUserSet = (e: React.FormEvent) => {
    e.preventDefault();
    if (user.trim()) {
      setIsUserSet(true);
    }
  };

  if (!isUserSet) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-900 text-white">
        <div className="w-full max-w-md p-8 space-y-8 bg-gray-800 rounded-2xl shadow-lg">
          <div className="text-center">
            <ChatBubbleLeftRightIcon className="w-16 h-16 mx-auto text-purple-400"/>
            <h1 className="text-4xl font-bold mt-4">Welcome to SignalR Chat</h1>
            <p className="mt-2 text-gray-400">Enter your username to start chatting</p>
          </div>
          <form onSubmit={handleUserSet} className="space-y-6">
            <div className="relative">
              <UserCircleIcon className="absolute w-6 h-6 text-gray-400 top-1/2 left-3 transform -translate-y-1/2"/>
              <input
                type="text"
                value={user}
                onChange={(e) => setUser(e.target.value)}
                placeholder="Enter your username"
                className="w-full pl-12 pr-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 outline-none transition"
              />
            </div>
            <button
              type="submit"
              disabled={!user.trim()}
              className="w-full py-3 px-4 bg-purple-600 hover:bg-purple-700 rounded-lg font-semibold transition disabled:bg-gray-500 disabled:cursor-not-allowed flex items-center justify-center"
            >
              Join Chat
            </button>
          </form>
        </div>
      </div>
    );
  }

  const renderConnectionStatus = () => {
    if (isConnecting) {
      return (
        <div className="flex items-center space-x-2 text-yellow-400">
          <ArrowPathIcon className="w-5 h-5 animate-spin" />
          <span>Connecting...</span>
        </div>
      );
    }
    if (isConnected) {
      return (
        <div className="flex items-center space-x-2 text-green-400">
          <WifiIcon className="w-5 h-5" />
          <span>Connected</span>
        </div>
      );
    }
    if (connectionError) {
      return (
        <div className="flex items-center space-x-2 text-red-400">
          <NoSymbolIcon className="w-5 h-5" />
          <span>Connection Failed</span>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="chat-container">
      <div className="sidebar p-4 space-y-6">
        <div>
          <h1 className="text-2xl font-bold flex items-center"><ChatBubbleLeftRightIcon className="w-8 h-8 mr-2"/> SignalR Chat</h1>
          <div className="mt-4 space-y-2">
            {renderConnectionStatus()}
            {connectionError && (
              <div className="text-sm text-red-300 bg-red-900 p-2 rounded-md">
                <p>{connectionError}</p>
                <button onClick={retryConnection} className="text-xs font-semibold mt-1 underline">Retry</button>
              </div>
            )}
          </div>
        </div>
        <div className="flex-grow">
          <div className="flex items-center justify-between">
             <h2 className="text-lg font-semibold">Online Users</h2>
             <div className="flex items-center space-x-2">
                <UsersIcon className="w-6 h-6 text-green-400"/>
                <span className="text-xl font-bold">{onlineUsers}</span>
             </div>
          </div>
        </div>
        <div className="border-t border-gray-700 pt-4">
            <div className="flex items-center space-x-3">
                <div className={`w-12 h-12 rounded-full bg-gradient-to-br ${getUserAvatarColor(user)} flex items-center justify-center text-xl font-bold`}>
                    {getUserInitials(user)}
                </div>
                <div>
                    <p className="font-semibold">{user}</p>
                    <p className="text-sm text-gray-400">You</p>
                </div>
            </div>
        </div>
      </div>

      <div className="main-content">
        <div className="chat-messages">
          {messages.map((msg, index) => (
            <div key={index} className={`flex items-start gap-3 my-4 ${msg.user === user ? 'flex-row-reverse' : ''}`}>
              <div className={`w-10 h-10 rounded-full bg-gradient-to-br ${getUserAvatarColor(msg.user)} flex items-center justify-center text-sm font-bold flex-shrink-0`}>
                {getUserInitials(msg.user)}
              </div>
              <div className={`p-3 rounded-lg max-w-lg ${msg.user === user ? 'bg-purple-600 text-white' : 'bg-gray-700 text-gray-200'}`}>
                <div className="flex items-center gap-2">
                  <p className="font-semibold">{msg.user}</p>
                  <p className="text-xs text-gray-400">{msg.timestamp}</p>
                </div>
                <p className="mt-1">{msg.message}</p>
                {msg.user === user && msg.status && (
                  <div className="flex justify-end mt-1">
                    {msg.status === 'sending' && <ArrowPathIcon className="w-4 h-4 text-gray-300 animate-spin" />}
                    {msg.status === 'sent' && <CheckCircleIcon className="w-4 h-4 text-gray-300" />}
                    {msg.status === 'delivered' && <CheckCircleIcon className="w-4 h-4 text-green-300" />}
                  </div>
                )}
              </div>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>
        <div className="message-input-form">
          <div className="h-6 px-2 text-sm text-gray-400">
            {typingUsers.length > 0 && (
              `${typingUsers.map(u => u.user).join(', ')} ${typingUsers.length === 1 ? 'is' : 'are'} typing...`
            )}
          </div>
          <form onSubmit={sendMessage} className="flex items-center space-x-4">
            <input
              type="text"
              value={message}
              onChange={(e) => {
                setMessage(e.target.value);
                handleTyping();
              }}
              placeholder="Type a message..."
              className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 outline-none transition text-white"
              disabled={!isConnected}
            />
            <button
              type="submit"
              className="p-3 bg-purple-600 rounded-full text-white hover:bg-purple-700 transition disabled:bg-gray-600 disabled:cursor-not-allowed"
              disabled={!message.trim() || !isConnected}
            >
              <PaperAirplaneIcon className="w-6 h-6" />
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default App;
